import request from "../../../../utils/request";

//查询单位信息分页
export function getUnitPageApi(params){
	return request({
		url:'/scm/base/unit/page',
		method:'GET',
		params
	})
}
//查询单位信息
export function getUnitApi(id){
	return request({
		url:'/scm/base/unit/get?id=' + id,
		method:'GET'
	})
}
//新增单位
export function createUnitApi(data){
	return request({
		url:'/scm/base/unit/create',
		method:'POST',
		data
	})
}
//修改单位
export function updateUnitApi(data){
	return request({
		url:'/scm/base/unit/update',
		method:'PUT',
		data
	})
}
//删除单位
export function deleteUnitApi(id){
	return request({
		url:'/scm/base/unit/delete?id=' + id,
		method:'DELETE'
	})
}