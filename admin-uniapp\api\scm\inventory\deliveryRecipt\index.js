import request from "../../../../utils/request";

//查询销售出库分页
export function getDeliveryReceiptPageApi(params){
	return request({
		url:'/scm/inventory/delivery-receipt/page',
		method:'GET',
		params
	})
}

//查询销售出库详情
export function getDeliveryReceiptApi(id){
	return request({
		url:'/scm/inventory/delivery-receipt/get?id=' + id,
		method:'GET'
	})
}

//新增销售出库
export function createDelieryReceiptApi(data){
	return request({
		url:'/scm/inventory/delivery-receipt/create',
		method:'POST',
		data
	})
}
//修改销售出库
export function updateDeliveryReceiptApi(data){
	return request({
		url:'/scm/inventory/delivery-receipt/update',
		method:'PUT',
		data
	})
}
//删除销售出库
export function deleteDeliveryReceiptApi(id){
	return request({
		url:'/scm/inventory/delivery-receipt/delete?id=' + id,
		method:'DELETE'
	})
}

// ==============================子表(销售出库明细)==========================
export function getDeliveryReceiptDetailListByBizOrderId(bizOrderId){
	return request({
		url:'/scm/inventory/delivery-receipt/delivery-receipt-detail/list-by-biz-order-id?bizOrderId=' + bizOrderId,
		method:'GET'
	})
}