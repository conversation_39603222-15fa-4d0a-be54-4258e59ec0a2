<template>
	<view class="select-picker-container">
		<!-- 选择框显示区域 -->
		<view class="selected-value">
			<text class="value-text text-ellipsis" @click="togglePicker">{{ formatDisplayText(displayText) }}</text>
			<view class="action-icons">
				<!-- 搜索图标 -->
				<uni-icons type="search" size="18" color="#999" @click="toggleSearchMode"></uni-icons>
				<!-- 下拉图标 -->
				<uni-icons type="bottom" size="18" color="#999" @click="togglePicker"></uni-icons>
			</view>
		</view>
		
		<!-- 常规选择器弹出层 -->
		<uni-popup ref="pickerPopup" type="bottom">
			<view class="picker-popup-content">
				<view class="picker-header">
					<text class="cancel-btn" @click="handleCancel">取消</text>
					<text class="title">{{ title }}</text>
					<text class="confirm-btn" @click="handleConfirm">确定</text>
				</view>
				<view class="picker-body">
					<view class="picker-scroll-view">
						<picker-view 
							class="custom-picker-view" 
							:indicator-style="indicatorStyle"
							:value="currentIndexes"
							@change="handleChange"
						>
							<picker-view-column>
								<view class="picker-item" v-for="(item, index) in innerOptions" :key="index">
									{{ formatDisplayText(getDisplayValue(item)) || '暂无数据' }}
								</view>
							</picker-view-column>
						</picker-view>
						
						<!-- 使用uni-load-more组件 -->
						<uni-load-more 
							v-if="enableLoadMore" 
							:status="loadMoreStatus" 
							:content-text="loadMoreText"
							@clickLoadMore="loadMoreData"
						></uni-load-more>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 全屏搜索模式弹出层 -->
		<uni-popup ref="searchPopup" type="center" background-color="#FFFFFF" :is-mask-click="false">
			<view class="search-popup-content">
				<!-- 搜索区域 -->
				<view class="search-header">
					<view class="search-bar">
						<uni-icons type="search" size="18" color="#999"></uni-icons>
						<input 
							class="search-input" 
							type="text" 
							v-model="searchKeyword" 
							placeholder="请输入名称查询" 
							confirm-type="search" 
							@confirm="handleSearch"
							@input="handleSearchInput"
						/>
						<uni-icons v-if="searchKeyword" type="clear" size="18" color="#999" @click="clearSearch"></uni-icons>
					</view>
					<view class="search-cancel" @click="closeSearchMode">取消</view>
				</view>
				
				<!-- 搜索结果列表 -->
				<scroll-view class="search-result-list" scroll-y @scrolltolower="handleResultScrollToLower">
					<view v-if="searching" class="search-status-message">
						<uni-load-more status="loading" :content-text="loadMoreText"></uni-load-more>
					</view>
					<view v-else-if="innerOptions.length === 0" class="search-status-message">
						<text class="empty-message">没有找到相关公司</text>
					</view>
					<view v-else class="search-result-items">
						<view 
							class="search-result-item" 
							v-for="(item, index) in innerOptions" 
							:key="index"
							@click="selectSearchItem(item)"
						>
							<text class="result-name">{{ formatDisplayText(getDisplayValue(item)) }}</text>
							<text class="result-code">{{ item.code }}</text>
						</view>
						<!-- 底部加载更多 -->
						<uni-load-more 
							v-if="enableLoadMore && innerOptions.length > 0" 
							:status="loadMoreStatus" 
							:content-text="loadMoreText"
							@clickLoadMore="loadMoreData"
						></uni-load-more>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import uniLoadMore from '../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue';
	import uniIcons from '../../uni_modules/uni-icons/components/uni-icons/uni-icons.vue';
	
	export default {
		name: 'SelectPicker',
		model: {
			prop: 'value',
			event: 'input'
		},
		components: {
			uniLoadMore,
			uniIcons
		},
		props: {
			options: {
				type: Array,
				default: () => []
			},
			value: {
				type: [String, Number, Object],
				default: null
			},
			title: {
				type: String,
				default: '请选择'
			},
			labelField: {
				type: String,
				default: 'name'
			},
			valueField: {
				type: String,
				default: 'id'
			},
			placeholder: {
				type: String,
				default: '请选择'
			},
			enableLoadMore: {
				type: Boolean,
				default: false
			},
			pageSize: {
				type: Number,
				default: 20
			},
			threshold: {
				type: Number,
				default: 5
			},
			// 最大显示文本长度
			maxTextLength: {
				type: Number,
				default: 15
			},
			disabled:{
				type:Boolean,
				default:false
			}
		},
		data() {
			return {
				innerValue: null,
				innerOptions: [],
				currentIndexes: [0],
				tempIndex: 0,
				indicatorStyle: 'height: 40px; line-height: 40px;',
				loading: false,
				noMoreData: false,
				currentPage: 1,
				loadMoreStatus: 'more', // 'more', 'loading', 'noMore'
				searchKeyword: '',
				searching: false,
				isSearchMode: false,
				searchDebounceTimer: null,
				localOptions: []
			}
		},
		computed: {
			displayText() {
				if (!this.innerValue && this.innerOptions.length === 0) {
					return this.placeholder;
				}
				
				if (!this.innerValue) {
					return this.placeholder;
				}
				
				if (typeof this.innerValue === 'object' && this.innerValue) {
					return this.innerValue[this.labelField] || this.placeholder;
				}
				
				const selectedOption = this.findOptionByValue(this.innerValue);
				return selectedOption ? this.getDisplayValue(selectedOption) : this.placeholder;
			},
			
			loadMoreText() {
				return {
					contentdown: null,
					contentrefresh: '加载中...',
					contentnomore: '没有更多数据'
				}
			}
		},
		watch: {
			value: {
				handler(newVal, oldVal) {
					// 只有当值真正改变时才更新
					if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
						this.updateInnerValue(newVal);
						this.$nextTick(() => {
							this.updateIndexFromValueIfNeeded();
						});
					}
				},
				deep: true,
				immediate: true
			},
			options: {
				handler(newVal, oldVal) {
					// 只有当选项真正改变时才更新
					if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
						// 创建内部选项的副本，避免修改原始props
						this.updateInnerOptions(newVal);

						this.$nextTick(() => {
							this.updateIndexFromValueIfNeeded();

							if (newVal && newVal.length > 0 && !this.noMoreData) {
								this.loadMoreStatus = 'more';
							}

							if (this.isSearchMode && this.searching) {
								this.searching = false;
							}
						});
					}
				},
				deep: true,
				immediate: true
			}
		},
		methods: {
			// 格式化显示文本，限制长度
			formatDisplayText(text) {
				if (!text) return '';
				return text.length > this.maxTextLength ? 
					text.substring(0, this.maxTextLength) + '...' : text;
			},
			
			// 辅助方法
			togglePopup(ref, isOpen = true) {
				this.$refs[ref][isOpen ? 'open' : 'close']();
			},
			
			togglePicker() {
				if(this.disabled){
					return
				}
				this.tempIndex = this.currentIndexes[0]; // 打开时重置临时索引为当前选中索引
				this.togglePopup('pickerPopup');
			},
			
			toggleSearchMode() {
				if(this.disabled) return
				this.isSearchMode = true;
				this.searchKeyword = '';
				this.togglePopup('searchPopup');
				this.resetLoadState();
			},
			
			closeSearchMode() {
				this.searchKeyword = '';
				this.togglePopup('searchPopup', false);
				
				setTimeout(() => {
					this.isSearchMode = false;
					this.resetLoadState();
					this.$emit('resetSearch');
				}, 200);
			},
			
			// 选择相关方法
			handleChange(e) {
				this.tempIndex = e.detail.value[0];
				this.checkAndLoadMoreIfNeeded();
			},
			
			handleCancel() {
				this.tempIndex = this.currentIndexes[0];
				this.togglePopup('pickerPopup', false);
			},
			
			handleConfirm() {
				// 确保当前索引有效
				if (this.tempIndex < 0 || this.tempIndex >= this.innerOptions.length) {
					this.tempIndex = 0;
				}
				
				this.updateSelectedValue(this.tempIndex);
				
				// 使用 nextTick 确保数据更新后再关闭弹窗
				this.$nextTick(() => {
					this.togglePopup('pickerPopup', false);
				});
			},
			
			updateSelectedValue(index) {
				if (index < 0 || index >= this.innerOptions.length) return;
				
				this.currentIndexes = [index];
				
				if (this.innerOptions.length > 0) {
					const selectedOption = this.innerOptions[index];
					if (!selectedOption) return;
					
					// 处理"暂不选择"选项，清空所选值
					if (selectedOption === '暂不选择') {
						// 更新内部值
						this.innerValue = null;
						// 通知父组件
						this.$emit('input', null);
						this.$emit('change', { value: null, index });
						return;
					}
					
					// 深拷贝选择的选项，不直接引用options中的对象
					const newValue = typeof selectedOption === 'object' ? 
						JSON.parse(JSON.stringify(selectedOption)) : selectedOption;
					
					// 更新内部值
					this.innerValue = newValue;
					// 通知父组件
					this.$emit('input', newValue);
					this.$emit('change', { value: newValue, index });
				}
			},
			
			selectSearchItem(item) {
				if (!item) return;
				
				// 深拷贝选中项，不直接引用options中的对象
				const newValue = typeof item === 'object' ? 
					JSON.parse(JSON.stringify(item)) : item;
				
				// 更新内部值
				this.innerValue = newValue;
				// 通知父组件
				this.$emit('input', newValue);
				this.$emit('change', { value: newValue, index: -1 });
				this.closeSearchMode();
			},
			
			// 搜索相关方法
			handleSearchInput() {
				clearTimeout(this.searchDebounceTimer);
				this.searchDebounceTimer = setTimeout(() => {
					if (this.searchKeyword.trim()) {
						this.handleSearch();
					}
				}, 500);
			},
			
			handleSearch() {
				if (!this.searchKeyword.trim()) return;
				
				this.searching = true;
				this.currentPage = 1;
				this.noMoreData = false;
				this.loadMoreStatus = 'loading';
				
				this.loadData();
			},
			
			clearSearch() {
				this.searchKeyword = '';
				this.resetLoadState();
				this.$emit('resetSearch');
			},
			
			// 加载数据相关方法
			handleResultScrollToLower() {
				this.checkAndLoadMoreIfNeeded(true);
			},
			
			checkAndLoadMoreIfNeeded(forceLoad = false) {
				const shouldLoad = this.enableLoadMore && !this.loading && !this.noMoreData && 
					(forceLoad || this.tempIndex >= this.innerOptions.length - this.threshold);
				
				if (shouldLoad) {
					this.loadMoreData();
				}
			},
			
			loadMoreData() {
				if (this.loading) return;
				
				this.loading = true;
				this.loadMoreStatus = 'loading';
				this.currentPage++;
				
				this.loadData();
			},
			
			loadData() {
				const params = {
					page: this.currentPage,
					pageSize: this.pageSize,
					callback: this.handleLoadCallback
				};
				
				if (this.isSearchMode && this.searchKeyword) {
					params.keyword = this.searchKeyword.trim();
					this.$emit('search', params);
				} else {
					this.$emit('loadMore', params);
				}
			},
			
			handleLoadCallback(result) {
				this.loading = false;
				
				if (!result.data || result.data.length === 0 || !result.hasMore) {
					this.noMoreData = true;
					this.loadMoreStatus = 'noMore';
				} else {
					this.loadMoreStatus = 'more';
				}
			},
			
			resetLoadState() {
				this.currentPage = 1;
				this.noMoreData = false;
				this.loading = false;
				this.loadMoreStatus = 'more';
				this.searchKeyword = '';
				this.searching = false;
			},
			
			// 数据处理方法
			updateIndexFromValueIfNeeded() {
				if (this.innerOptions.length > 0 && this.innerValue) {
					this.updateIndexFromValue();
				}
			},
			
			updateIndexFromValue() {
				if (!this.innerValue || this.innerOptions.length === 0) {
					this.currentIndexes = [0];
					this.tempIndex = 0;
					return;
				}
				
				const index = this.findIndexByValue(this.innerValue);
				this.currentIndexes = [index !== -1 ? index : 0];
				this.tempIndex = this.currentIndexes[0];
			},
			
			findIndexByValue(value) {
				if (!value || this.innerOptions.length === 0) return 0; // 返回0，对应"暂不选择"
				
				if (typeof value === 'object') {
					const valueToFind = value[this.valueField];
					return this.innerOptions.findIndex(item => 
						typeof item === 'object' && 
						String(item[this.valueField]) === String(valueToFind)
					);
				} else {
					return this.innerOptions.findIndex(item => 
						(typeof item === 'object' ? 
							String(item[this.valueField]) : String(item)) === String(value)
					);
				}
			},
			
			findOptionByValue(value) {
				const index = this.findIndexByValue(value);
				return index !== -1 ? this.innerOptions[index] : null;
			},
			
			// 获取显示值，特殊处理"暂不选择"选项
			getDisplayValue(item) {
				if (item === '暂不选择') {
					return '暂不选择';
				}
				return typeof item === 'object' ? item[this.labelField] : item;
			},
			
			// 更新内部选项，避免直接修改props
			updateInnerOptions(newOptions) {
				try {
					// 重置内部选项，始终包含"暂不选择"
					let tempOptions = ['暂不选择'];

					// 如果有新选项，则添加到"暂不选择"之后
					if (Array.isArray(newOptions) && newOptions.length > 0) {
						// 对数组进行深拷贝，确保不会修改原始数据
						const optionsCopy = newOptions.map(item => {
							if (typeof item === 'object' && item !== null) {
								// 使用JSON深拷贝，确保完全独立
								return JSON.parse(JSON.stringify(item));
							} else {
								return item;
							}
						});

						tempOptions = tempOptions.concat(optionsCopy);
					}

					// 使用Vue.set或直接赋值，确保响应式更新
					this.$set(this, 'innerOptions', tempOptions);
				} catch (error) {
					console.warn('SelectPicker updateInnerOptions error:', error);
					// 出错时设置默认值
					this.$set(this, 'innerOptions', ['暂不选择']);
				}
			},

			// 在watch中正确处理value变化时更新innerValue
			updateInnerValue(newVal) {
				try {
					// 深拷贝来避免直接引用，防止修改内部值影响props
					if (newVal !== null && newVal !== undefined) {
						if (typeof newVal === 'object') {
							// 对对象进行深拷贝
							this.innerValue = JSON.parse(JSON.stringify(newVal));
						} else {
							// 对基本类型直接赋值
							this.innerValue = newVal;
						}
					} else {
						this.innerValue = null;
					}
				} catch (error) {
					console.warn('SelectPicker updateInnerValue error:', error);
					this.innerValue = null;
				}
			}
		},
		created() {
			// 初始化时设置innerOptions，避免直接修改props
			this.updateInnerValue(this.value);
			this.updateInnerOptions(this.options);
		}
	}
</script>

<style>
/* 选择器容器 */
.select-picker-container {
	width: 100%;
	box-sizing: border-box;
}

/* 选择框样式 */
.selected-value {
	height: 40px;
	line-height: 40px;
	padding: 0 10px;
	background-color: #fff;
	border: 1px solid #eee;
	border-radius: 4px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	box-sizing: border-box;
}

.value-text {
	flex: 1;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	max-width: 75%; /* 更严格限制宽度 */
}

.text-ellipsis {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.action-icons {
	display: flex;
	align-items: center;
	gap: 10px;
	flex-shrink: 0; /* 防止图标被压缩 */
	width: 60px; /* 固定宽度 */
	justify-content: flex-end;
}

/* 弹出层样式 */
.picker-popup-content {
	background-color: #fff;
	border-top-left-radius: 12px;
	border-top-right-radius: 12px;
	overflow: hidden;
}

.picker-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 45px;
	padding: 0 15px;
	border-bottom: 1px solid #eee;
}

.picker-header .title {
	font-size: 16px;
	color: #333;
	max-width: 160px; /* 更严格限制标题长度 */
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.picker-header .cancel-btn,
.picker-header .confirm-btn {
	font-size: 14px;
	padding: 10px 5px;
	min-width: 50px; /* 确保按钮有足够宽度 */
	text-align: center;
}

.picker-header .cancel-btn {
	color: #666;
}

.picker-header .confirm-btn {
	color: #007AFF;
}

.picker-body {
	height: 240px;
}

.picker-scroll-view {
	display: flex;
	flex-direction: column;
	height: 100%;
}

.custom-picker-view {
	width: 100%;
	height: 210px; /* 留出30px给load-more组件 */
}

.picker-item {
	height: 40px;
	line-height: 40px;
	text-align: center;
	font-size: 16px;
	color: #333;
	padding: 0 15px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	box-sizing: border-box;
	width: 100%;
}

/* 全屏搜索模式样式 */
.search-popup-content {
	width: 100vw;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #fff;
}

.search-header {
	padding: 10px 15px;
	display: flex;
	align-items: center;
	border-bottom: 1px solid #eee;
}

.search-bar {
	flex: 1;
	height: 36px;
	background-color: #f5f5f5;
	border-radius: 18px;
	display: flex;
	align-items: center;
	padding: 0 15px;
}

.search-input {
	flex: 1;
	height: 36px;
	line-height: 36px;
	background-color: transparent;
	border: none;
	margin: 0 10px;
	font-size: 14px;
}

.search-cancel {
	padding: 0 10px;
	font-size: 14px;
	color: #007AFF;
	min-width: 40px;
	text-align: center;
}

.search-result-list {
	flex: 1;
	overflow-y: auto;
}

.search-status-message {
	padding: 20px 0;
	text-align: center;
}

.empty-message {
	font-size: 14px;
	color: #999;
}

.search-result-items {
	padding: 0 15px;
}

.search-result-item {
	padding: 15px 0;
	border-bottom: 1px solid #eee;
	width: 100%;
	box-sizing: border-box;
}

.result-name {
	font-size: 16px;
	color: #333;
	margin-bottom: 5px;
	display: block;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	width: 100%;
}

.result-code {
	font-size: 14px;
	color: #999;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	width: 100%;
}
</style> 