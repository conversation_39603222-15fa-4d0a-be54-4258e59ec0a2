import request from "../../../../utils/request";

//查询采购订单分页
export function getPurchaseOrderPageApi(params){
	return request({
		url:'/scm/purchase/order/page',
		method:'GET',
		params
	})
}
//查询采购订单详情
export function getPurchaseOrderApi(id){
	return request({
		url:'/scm/purchase/order/get?id=' + id,
		method:'GET'
	})
}
//新增采购订单
export function createPurchaseOrderApi(data){
	return request({
		url:'/scm/purchase/order/create',
		method:'POST',
		data
	})
}
//修改采购订单
export function updatePurchaseOrderApi(data){
	return request({
		url:'/scm/purchase/order/update',
		method:'PUT',
		data
	})
}
//删除采购订单
export function deletePurchaseOrderApi(id){
	return request({
		url:'/scm/purchase/order/delete?id=' + id,
		method:'DELETE'
	})
}
//获得采购订单产品列表
export function getPurchaseOrderDetailListByOrderId(orderId){
	return request({
		url:'/scm/purchase/order/order-detail/list-by-order-id?orderId=' + orderId,
		method:'GET'
	})
}