import request from "../../../../../utils/request";

//查询科目列表
export function getAccountSubjectListApi(params){
	return request({
		url:'/scm/finance/account-subject/list',
		method:'GET',
		params
	})
}
//查询科目详情
export function getAccountSubjectApi(id){
	return request({
		url:'/scm/finance/account-subject/get?id=' + id,
		method:'GET'
	})
}
//新增科目
export function createAccountSubjectApi(data){
	return request({
		url:'/scm/finance/account-subject/create',
		method:'POST',
		data
	})
}
//修改科目
export function updateAccountSubjectApi(data){
	return request({
		url:'/scm/finance/account-subject/update',
		method:'PUT',
		data
	})
}
//删除科目
export function deleteAccountSubjectApi(id){
	return request({
		url:'/scm/finance/account-subject/delete?id=' + id,
		method:'DELETE'
	})
}
//查询科目基本信息列表
export function getSimpleAccountSubjectListApi(params){
	return request({
		url:'/scm/finance/account-subject/simple-page',
		method:'GET',
		params
	})
}
//查询科目列表
export function getAccountSubjectPageApi(params){
	return request({
		url:'/scm/finance/account-subject/page',
		method:'GET',
		params
	})
}
//查询科目树形结构
export function getAccountSubjectTreeApi(){
	return request({
		url:'/scm/finance/account-subject/tree',
		method:'GET',
		params:{
			detail:0
		}
	})
}