import request from "../../../../utils/request";

//查询物料信息分类
export function getMaterialPageApi(params){
	return request({
		url:'/scm/base/material/page',
		method:'GET',
		params,
	})
}

//查询物料信息详情
export function getMaterialApi(id){
	return request({
		url:`/scm/base/material/get?id=` + id,
		method:'GET'
	})
}
//新增物料信息
export function createMaterialApi(data){
	return request({
		url:'/scm/base/material/create',
		method:'POST',
		data
	})
}
//修改物料信息
export function updateMaterialApi(data){
	return request({
		url:'/scm/base/material/update',
		method:'PUT',
		data
	})
}
//删除物料信息
export function deleteMaterialApi(id){
	return request({
		url:`/scm/base/material/delete?id=` + id,
		method:'DELETE'
	})
}
