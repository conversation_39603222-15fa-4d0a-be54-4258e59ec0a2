export interface InventoryDetail {
  id?: string | number;
  num?: string | number;
  bizOrderId?: string | number;
  bizOrderNo?: string;
  warehouseId?: string | number;
  locationId?: string | number;
  materialId?: string | number;
  materialName?: string;
  materialCode?: string;
  unit?: string;
  unitName?: string;
  unitPrice?: string | number;
  amount?: string | number;
  remark?: string;
  // 新的字段名（与接口数据一致）
  plannedQuantity?: string | number;
  fulfilledQuantity?: string | number;
  standardPlannedQuantity?: string | number;
  standardFulfilledQuantity?: string | number;
  standardUnit?: string;
  // 保留旧字段名以兼容其他模块
  receivableQuantity?: string | number;
  receivedQuantity?: string | number;
  auxReceivableQuantity?: string | number;
  auxReceivedQuantity?: string | number;
  auxUnit?: string;
  taxPrice?: string | number;
  taxAmount?: string | number;
  invoiceQuantity?: string | number;
  invoiceAmount?: string | number;
  // 新的字段名
  standardInvoiceQuantity?: string | number;
  // 保留旧字段名以兼容其他模块
  invoiceAuxQuantity?: string | number;
  effictiveDate?: number;
  expiryDate?: number;
  note?: string;
  sourceId?: string | number;
  sourceNo?: string;
  batchNo?: string;
  costObjectId?: string | number;
  costObjectName?: string;
  accountingVoucherNumber?: string;
  [key: string]: any;
}