import request from "../../../../utils/request";

export function getApplyPageApi(params){
	return request({
		url:'/scm/quote/apply/page',
		"method":'GET',
		params,
	})
}
export function deleteApplyApi(id){
	return request({
		url:`/scm/quote/apply/delete?id=` + id,
		"method":'DELETE',
	})
}
export function confirmApplyApi(data){
	return request({
		url:'/scm/quote/apply/confirm',
		"method":'POST',
		data,
	})
}
export function createApplyApi(data){
	return request({
		url:'/scm/quote/apply/create',
		"method":'POST',
		data,
	})
}

export function updateApplyApi(data){
	return request({
		url:'/scm/quote/apply/update',
		"method":'PUT',
		data,
	})
}