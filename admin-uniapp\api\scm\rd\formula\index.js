import request from "../../../../utils/request";

//查询配方信息分页
export function getFormulaPageApi(params){
	if(params){
		params.quoteFlag = 0
	}
	return request({
		url:'/scm/rd/formula/page',
		method:'GET',
		params
	})
}

//查询配方信息详情
export function getFormulaApi(id,refresh){
	return request({
		url:`/scm/rd/formula/get?id=` + id + `&refresh=` + refresh,
		method:'GET'
	})
}
export function createFormulaApi(data){
	return request({
		url:'/scm/rd/formula/create',
		method:'POST',
		data
	})
}
export function updateFormulaApi(data){
	return request({
		url:'/scm/rd/formula/update',
		method:'PUT',
		data
	})
}
export function deleteFormuaApi(id){
	return request({
		url:'/scm/rd/formula/delete?id=' + id,
		method:'DELETE'
	})
}

export function approveFormulaApi(data){
	return request({
		url:'/scm/rd/formula/approve',
		method:'POST',
		data
	})
}
