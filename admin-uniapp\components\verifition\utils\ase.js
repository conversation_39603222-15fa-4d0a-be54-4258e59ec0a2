import AES from 'crypto-js/aes'
import Utf8 from 'crypto-js/enc-utf8'
import ECB from 'crypto-js/mode-ecb'
import Pkcs7 from 'crypto-js/pad-pkcs7'

/**
 * @word 要加密的内容
 * @keyWord String  服务器随机返回的关键字
 *  */
export function aesEncrypt(word, keyWord = "XwKsGlMcdPMEhR1B") {
	var key = Utf8.parse(keyWord);
	var srcs = Utf8.parse(word);
	var encrypted = AES.encrypt(srcs, key, {
		mode: ECB,
		padding: Pkcs7
	});
	return encrypted.toString();
}
