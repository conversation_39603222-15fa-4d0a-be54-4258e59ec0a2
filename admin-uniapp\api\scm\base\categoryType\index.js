import request from "../../../../utils/request";

//查询分类类型分页
export function getCategoryTypePageApi(params){
	return request({
		url:'/scm/base/category-type/page',
		method:'GET',
		params
	})
}
//查询分类类型详情
export function getCategoryTypeApi(id){
	return request({
		url:'/scm/base/category-type/get?id=' + id,
		method:'GET'
	})
}
//新增分类类型
export function createCategoryTypeApi(data){
	return request({
		url:'/scm/base/category-type/create',
		method:'POST',
		data
	})
}
//修改分类类型
export function updateCategoryTypeApi(data){
	return request({
		url:'/scm/base/category-type/update',
		method:'PUT',
		data
	})
}
//删除分类类型
export function deleteCategoryTypeApi(id){
	return request({
		url:'/scm/base/category-type/delete?id=' + id,
		method:'DELETE'
	})
}