import request from "../../../../utils/request";

//查询采购入库分页
export function getPurchaseReceiptPageApi(params){
	return request({
		url:'/scm/inventory/purchase-receipt/page',
		method:'GET',
		params
	})
}

//查询采购入库详情
export function getPurchaseReceiptApi(id){
	return request({
		url:'/scm/inventory/purchase-receipt/get?id=' + id,
		method:'GET'
	})
}

//新增采购入库
export function createPurchaseReceiptApi(data){
	return request({
		url:'/scm/inventory/purchase-receipt/create',
		method:'POST',
		data
	})
}
//修改采购入库
export function updatePurchaseReceiptApi(data){
	return request({
		url:'/scm/inventory/purchase-receipt/update',
		method:'PUT',
		data
	})
}

//删除采购入库
export function deletePurchaseReceiptApi(id){
	return request({
		url:'/scm/inventory/purchase-receipt/delete?id=' + id,
		method:'DELETE'
	})
}

// ===================子表(采购入库明细)=================
export function getPurchaseReceiptDetailListByBizOrderId(bizOrderId){
	return request({
		url:'/scm/inventory/purchase-receipt/purchase-receipt-detail/list-by-biz-order-id?bizOrderId=' + bizOrderId,
		method:'GET'
	})
}