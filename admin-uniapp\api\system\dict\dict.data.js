import request from "../../../utils/request"

// 查询字典数据（精简)列表
export const getSimpleDictDataList = () => {
  return request({ url: '/system/dict-data/simple-list', method: 'GET' });
}

// 查询字典数据列表
export const getDictDataPage = (params) => {
  return request({ url: '/system/dict-data/page', method: 'GET', params });
}

// 查询字典数据详情
export const getDictData = (id) => {
  return request({ url: '/system/dict-data/get', method: 'GET', params: { id } });
}

// 新增字典数据
export const createDictData = (data) => {
  return request({ url: '/system/dict-data/create', method: 'POST', data });
}

// 修改字典数据
export const updateDictData = (data) => {
  return request({ url: '/system/dict-data/update', method: 'PUT', data });
}

// 删除字典数据
export const deleteDictData = (id) => {
  return request({ url: '/system/dict-data/delete', method: 'DELETE', params: { id } });
}

// 导出字典类型数据
export const exportDictData = (params) => {
  return request({ url: '/system/dict-data/export',method:'DOWNLOAD',params })
}
