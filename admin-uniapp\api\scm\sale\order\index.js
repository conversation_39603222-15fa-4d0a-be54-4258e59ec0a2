import request from "../../../../utils/request";

//查询销售订单分页
export function getOrderPageApi(params){
	return request({
		url:'/scm/sale/order/page',
		method:'GET',
		params
	})
}
//查询销售订单详情
export function getOrderApi(id,detail){
	return request({
		url:'/scm/sale/order/get?id=' + id + `&detail=` + detail,
		method:'GET'
	})
}
//新增销售订单
export function createOrderApi(data){
	return request({
		url:'/scm/sale/order/create',
		method:'POST',
		data
	})
}
//修改销售订单
export function updateOrderApi(data){
	return request({
		url:'/scm/sale/order/update',
		method:'PUT',
		data
	})
}
//删除销售订单
export  function deleteOrderApi(id){
	return request({
		url:'/scm/sale/order/delete?id=' + id,
		method:'DELETE'
	})
}

// ==============子表(订单商品)=======================

//获得订单商品分页
export function getOrderDetailPageApi(params){
	return request({
		url:'/scm/sale/order/order-detail/page',
		method:'GET',
		params
	})
}

//新增订单商品
export function createOrderDetailApi(data){
	return request({
		url:'/scm/sale/order/order-detail/create',
		method:'PSOT',
		data
	})
}

//修改订单商品
export function updateOrderDetailApi(data){
	return request({
		url:'/scm/sale/order/order-detail/update',
		method:'PUT',
		data
	})
}
//删除订单商品
export function deleteOrderDetailApi(id){
	return request({
		url:'/scm/sale/order/order-detail/delete?id=' + id,
		method:'DELETE'
	})
}
//获得订单商品
export function getOrderDetailApi(id){
	return request({
		url:'/scm/sale/order/order-detail/get?id=' + id,
		method:'GET'
	})
}
export function getOrderDetailListByOrderId(data,orderId){
	return request({
		url:'/scm/sale/order/order-detail/page?orderId=' + orderId,
		method:'GET',
		data
	})
}
