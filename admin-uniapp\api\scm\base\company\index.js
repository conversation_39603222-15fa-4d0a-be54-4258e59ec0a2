import request from "../../../../utils/request";

//获取公司分页信息
 export function getCompanyPageApi(params){
	 return request({
		 url:'/scm/base/company/page',
		 method:'GET',
		 params
	 })
 }
 
 //获取公司详细信息
 export function getCompanyApi(id){
	 return request({
		 url:`/scm/base/company/get?id=` + id,
		 method:'GET'
	 })
 }
 
 //新增公司信息
 export function createCompanyApi(data){
	 return request({
		 url:'/scm/base/company/create',
		 method:'POST',
		 data
	 })
 }
 
 //更新公司信息
 export function updateCompanyApi(data){
	return request({
		url:'/scm/base/company/update',
		method:'PUT',
		data
	})
 }
 
 //删除公司信息
 export function deleteCompanyApi(id){
	 return request({
		 url:`/scm/base/company/delete?id=` + id,
		 method:'DELETE'
	 })
 }