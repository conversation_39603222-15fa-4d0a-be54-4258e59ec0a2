<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="auto"
    >
      <el-form-item label="单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="业务类型" prop="bizType">
        <el-select
          v-model="queryParams.bizType"
          placeholder="请选择业务类型"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="来源类型" prop="sourceType">
        <el-select
          v-model="queryParams.sourceType"
          placeholder="请选择来源类型"
          clearable
          class="!w-240px"
        >
          <el-option 
          v-for="item in material_source" 
          :key="item.value" 
          :label="item.label" 
          :value="item.value" 
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="来源单ID" prop="sourceId">
        <el-input
          v-model="queryParams.sourceId"
          placeholder="请输入来源单ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="来源单编号" prop="sourceNo">
        <el-input
          v-model="queryParams.sourceNo"
          placeholder="请输入来源单编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="交易对象ID" prop="objectId">
        <el-input
          v-model="queryParams.objectId"
          placeholder="请输入交易对象ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="交易对象名称" prop="objectName">
        <el-input
          v-model="queryParams.objectName"
          placeholder="请输入交易对象名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="交易对象订单号" prop="objectOrderNo">
        <el-input
          v-model="queryParams.objectOrderNo"
          placeholder="请输入交易对象订单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="交易日期" prop="date">
        <el-date-picker
          v-model="queryParams.date"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="仓库ID" prop="warehouseId">
        <el-input
          v-model="queryParams.warehouseId"
          placeholder="请输入仓库ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <!-- <el-form-item label="科目ID" prop="accountId">
        <el-input
          v-model="queryParams.accountId"
          placeholder="请输入科目ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="摘要" prop="note">
        <el-input
          v-model="queryParams.note"
          placeholder="请输入摘要"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="审批状态" prop="approveStatus">
        <el-select
          v-model="queryParams.approveStatus"
          placeholder="请选择审批状态"
          clearable
          class="!w-240px"
        >
          <el-option 
          v-for="item in approve_status" 
          :key="item.value" 
          :label="item.label" 
          :value="item.value" 
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审批单号" prop="approveNo">
        <el-input
          v-model="queryParams.approveNo"
          placeholder="请输入审批单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="审批人ID" prop="approverId">
        <el-input
          v-model="queryParams.approverId"
          placeholder="请输入审批人ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="审批人" prop="approverName">
        <el-input
          v-model="queryParams.approverName"
          placeholder="请输入审批人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="审批时间" prop="approveDate">
        <el-date-picker
          v-model="queryParams.approveDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="部门ID" prop="deptId">
        <el-input
          v-model="queryParams.deptId"
          placeholder="请输入部门ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="业务员ID" prop="empId">
        <el-input
          v-model="queryParams.empId"
          placeholder="请输入业务员ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="管理员ID" prop="managerId">
        <el-input
          v-model="queryParams.managerId"
          placeholder="请输入管理员ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="管理员1ID" prop="manger1Id">
        <el-input
          v-model="queryParams.manger1Id"
          placeholder="请输入管理员1ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="记账ID" prop="accountantId">
        <el-input
          v-model="queryParams.accountantId"
          placeholder="请输入记账ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="检验员ID" prop="checkerId">
        <el-input
          v-model="queryParams.checkerId"
          placeholder="请输入检验员ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['inventory:purchase-receipt:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['inventory:purchase-receipt:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- 子表的列表 -->
      <el-table-column type="expand">
        <template #default="scope">
          <el-tabs model-value="purchaseReceiptDetail">
            <el-tab-pane label="采购入库明细" name="purchaseReceiptDetail">
              <PurchaseReceiptDetailList :biz-order-id="scope.row.id" />
            </el-tab-pane>
          </el-tabs>
        </template>
      </el-table-column>
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="单号" align="center" prop="orderNo" />
      <el-table-column label="业务类型" align="center" prop="bizType" />
      <el-table-column label="来源类型" align="center" prop="sourceType">
        <template #default="scope">
          {{ getDictLabel('MATERIAL_SOURCE', scope.row.sourceType) }}
        </template>
      </el-table-column>
      <el-table-column label="来源单ID" align="center" prop="sourceId" width="120px"/>
      <el-table-column label="来源单编号" align="center" prop="sourceNo" width="100px"/>
      <el-table-column label="交易对象ID" align="center" prop="objectId" width="120px"/>
      <el-table-column label="交易对象名称" align="center" prop="objectName" width="120px"/>
      <el-table-column label="交易对象订单号" align="center" prop="objectOrderNo" width="140px"/>
      <el-table-column
        label="交易日期"
        align="center"
        prop="date"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="仓库ID" align="center" prop="warehouseId" />
      <el-table-column label="科目ID" align="center" prop="accountId" />
      <el-table-column label="摘要" align="center" prop="note" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="审批状态" align="center" prop="approveStatus">
        <template #default="scope">
          {{ getDictLabel('APPROVE_STATUS', scope.row.approveStatus) }}
        </template>
      </el-table-column>
      <el-table-column label="审批单号" align="center" prop="approveNo" />
      <el-table-column label="审批人ID" align="center" prop="approverId" width="120px"/>
      <el-table-column label="审批人" align="center" prop="approverName" />
      <el-table-column
        label="审批时间"
        align="center"
        prop="approveDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="部门ID" align="center" prop="deptId" />
      <el-table-column label="业务员ID" align="center" prop="empId" width="120px"/>
      <el-table-column label="管理员ID" align="center" prop="managerId" width="120px"/>
      <el-table-column label="管理员1ID" align="center" prop="manger1Id" width="120px"/>
      <el-table-column label="记账ID" align="center" prop="accountantId"/>
      <el-table-column label="检验员ID" align="center" prop="checkerId" width="120px"/>
      <el-table-column label="操作" align="center" min-width="120px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['inventory:purchase-receipt:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['inventory:purchase-receipt:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <PurchaseReceiptForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { PurchaseReceiptApi, PurchaseReceiptVO } from '@/api/scm/inventory/purchasereceipt'
import PurchaseReceiptForm from './PurchaseReceiptForm.vue'
import PurchaseReceiptDetailList from './components/PurchaseReceiptDetailList.vue'
import { DICT_TYPE,getStrDictOptions,getDictLabel } from '@/utils/dict'
/** 采购入库 列表 */
defineOptions({ name: 'PurchaseReceipt' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<PurchaseReceiptVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderNo: undefined,
  bizType: undefined,
  sourceType: undefined,
  sourceId: undefined,
  sourceNo: undefined,
  objectId: undefined,
  objectName: undefined,
  objectOrderNo: undefined,
  date: [],
  warehouseId: undefined,
  accountId: undefined,
  note: undefined,
  remark: undefined,
  createTime: [],
  approveStatus: undefined,
  approveNo: undefined,
  approverId: undefined,
  approverName: undefined,
  approveDate: [],
  deptId: undefined,
  empId: undefined,
  managerId: undefined,
  manger1Id: undefined,
  accountantId: undefined,
  checkerId: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const material_source = getStrDictOptions(DICT_TYPE.MATERIAL_SOURCE)
const approve_status = getStrDictOptions(DICT_TYPE.APPROVE_STATUS)
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await PurchaseReceiptApi.getPurchaseReceiptPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PurchaseReceiptApi.deletePurchaseReceipt(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PurchaseReceiptApi.exportPurchaseReceipt(queryParams)
    download.excel(data, '采购入库.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
