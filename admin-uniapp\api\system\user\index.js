import request from "../../../utils/request";

// 查询用户精简信息列表
export function getSimpleUserListApi() {
	return request({
		url: '/system/user/simple-list',
		method: 'GET'
	})
}

// 查询用户管理列表
export function getUserPageApi(params) {
	return request({
		url: '/system/user/page',
		method: 'GET',
		params
	})
}

// 查询用户详情
export function getUserApi(id) {
	return request({
		url: '/system/user/get?id=' + id,
		method: 'GET'
	})
}

// 新增用户
export function createUserApi(data) {
	return request({
		url: '/system/user/create',
		method: 'POST',
		data
	})
}

// 修改用户
export function updateUserApi(data) {
	return request({
		url: '/system/user/update',
		method: 'PUT',
		data
	})
}

// 删除用户
export function deleteUserApi(id) {
	return request({
		url: '/system/user/delete?id=' + id,
		method: 'DELETE'
	})
}
