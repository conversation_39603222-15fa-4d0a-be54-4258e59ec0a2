import request  from "../../../utils/request";

// 查询字典（精简)列表
export function getSimpleDictTypeList(){
  return request({ url: '/system/dict-type/list-all-simple', method: 'GET' });
}

// 查询字典列表
export function getDictTypePage(params){
  return request({ url: '/system/dict-type/page', method: 'GET', params });
}

// 查询字典详情
export function getDictType(id){
  return request({ url: '/system/dict-type/get', method: 'GET', params: { id } });
}

// 新增字典
export const createDictType = (data) => {
  return request({ url: '/system/dict-type/create', method: 'POST', data });
}

// 修改字典
export const updateDictType = (data) => {
  return request({ url: '/system/dict-type/update', method: 'PUT', data });
}

// 删除字典
export const deleteDictType = (id) => {
  return request({ url: '/system/dict-type/delete', method: 'DELETE', params: { id } });
}
// 导出字典类型
export const exportDictType = (params) => {
  return request({ url: '/system/dict-type/export',method:'DOWNLOAD', params })
}
