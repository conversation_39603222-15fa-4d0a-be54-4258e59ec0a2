import request from "../../../../utils/request";

//查询分类列表
export function getCategoryListApi(params){
	return request({
		url:'/scm/base/category/list',
		method:'GET',
		params
	})
}
//查询分类详情
export function getCategoryApi(id){
	return request({
		url:'/scm/base/category/get?id=' + id,
		method:'GET'
	})
}
//新增分类
export function createCategoryApi(data){
	return request({
		url:'/scm/base/category/create',
		method:'POST',
		data
	})
}
//修改分类
export function updateCategoryApi(data){
	return request({
		url:'/scm/base/category/update',
		method:'PUT',
		data
	})
}
//删除分类
export function deleteCategoryApi(id){
	return request({
		url:'/scm/base/category/delete?id=' + id,
		method:'DELETE'
	})
}