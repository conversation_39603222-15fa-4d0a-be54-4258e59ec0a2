import request from "../../../../utils/request";

//查询仓库信息列表
export function getWarehouseListApi(params){
	return request({
		url:'/scm/inventory/warehouse/list',
		method:'GET',
		params
	})
}

//查询仓库信息详情
export function getWarehouseApi(id){
	return request({
		url:'/scm/inventory/warehouse/get?id=' + id,
		method:'GET'
	})
}
//新增仓库信息
export function createWarehouseApi(data){
	return request({
		url:'/scm/inventory/warehouse/create',
		method:'POST',
		data
	})
}
//修改仓库信息
export function updateWarehouseApi(data){
	return request({
		url:'/scm/inventory/warehouse/update',
		method:'PUT',
		data
	})
}
//删除仓库信息
export function deleteWarehouseApi(id){
	return request({
		url:'/scm/inventory/warehouse/delete?id=' + id,
		method:'DELETE'
	})
}