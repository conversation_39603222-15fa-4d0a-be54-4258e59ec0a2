<template>
  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="序号" align="center" prop="num" />
       <el-table-column label="单号" align="center" prop="bizOrderNo" />
      <el-table-column label="仓库ID" align="center" prop="warehouseId" />
      <el-table-column label="库位ID" align="center" prop="locationId" />
      <el-table-column label="物料ID" align="center" prop="materialId" />
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="物料编号" align="center" prop="materialCode" />
      <el-table-column label="单位" align="center" prop="unit" />
      <el-table-column label="单价" align="center" prop="unitPrice" />
      <el-table-column label="金额" align="center" prop="amount" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="应购数量" align="center" prop="plannedQuantity" />
      <el-table-column label="实收数量" align="center" prop="fulfilledQuantity" />
      <el-table-column label="基本单位应收数量" align="center" prop="standardPlannedQuantity" />
      <el-table-column label="基本单位实收数量" align="center" prop="standardFulfilledQuantity" />
      <el-table-column label="基本单位" align="center" prop="standardUnit" />
      <el-table-column label="含税单价" align="center" prop="taxPrice" />
      <el-table-column label="含税金额" align="center" prop="taxAmount" />
      <el-table-column label="开票数量" align="center" prop="invoiceQuantity" />
      <el-table-column label="开票金额" align="center" prop="invoiceAmount" />
      <el-table-column label="开票基本数量" align="center" prop="standardInvoiceQuantity" />
      <el-table-column
        label="生产日期"
        align="center"
        prop="effictiveDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="失效日期"
        align="center"
        prop="expiryDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="说明" align="center" prop="note" />
      <el-table-column label="源单ID" align="center" prop="sourceId" />
      <el-table-column label="源单单号" align="center" prop="sourceNo" />
      <el-table-column label="批号" align="center" prop="batchNo" />
      <el-table-column label="成本对象编码" align="center" prop="costObjectId" />
      <el-table-column label="成本对象名称" align="center" prop="costObjectName" />
      <el-table-column label="记账凭证号" align="center" prop="accountingVoucherNumber" />
    </el-table>
  </ContentWrap>
</template>
<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { BizOrderApi } from '@/api/scm/inventory/bizorder'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const props = defineProps<{
  bizOrderId?: number // 订单编号（主表的关联字段）
}>()
const loading = ref(false) // 列表的加载中
const list = ref([]) // 列表的数据

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    list.value = await BizOrderApi.getBizOrderDetailListByBizOrderId(props.bizOrderId)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
