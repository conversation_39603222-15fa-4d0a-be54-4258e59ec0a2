import request from "../../../../utils/request";

//查询库位信息分页
export function getWarehouseLocationPageApi(params){
	return request({
		url:'/scm/inventory/warehouse-location/page',
		method:'GET',
		params
	})
}

//查询库位信息详情
export function getWarehouseLocationApi(id){
	return request({
		url:'/scm/inventory/warehouse-location/get?id=' + id,
		method:'GET'
	})
}

//新增库位信息
export function createWarehouseLocationApi(data){
	return request({
		url:'/scm/inventory/warehouse-location/create',
		method:'POST',
		data
	})
}

//修改库位信息
export function updateWarehouseLocationApi(data){
	return request({
		url:'/scm/inventory/warehouse-location/update',
		method:'PUT',
		data
	})
}

//删除库位信息
export function deleteWarehouseLocationApi(id){
	return request({
		url:'/scm/inventory/warehouse-location/delete?id=' + id,
		method:'DELETE'
	})
}
