import request from "../../../../utils/request";

//查询商品价格分页
export function getMaterialPricePageApi(params){
	return request({
		url:'/scm/base/material-price/page',
		method:'GET',
		params
	})
}

//查询商品价格详情
export function getMaterialPriceApi(id){
	return request({
		url:'/scm/base/material-price/get?id=' + id,
		method:'GET'
	})
}
//新增商品价格
export function createMaterialPriceApi(data){
	return request({
		url:'/scm/base/material-price/create',
		method:'POST',
		data,
	})
}

//修改商品价格
export function updateMaterialPriceApi(data){
	return request({
		url:'/scm/base/material-price/update',
		method:'POST',
		data
	})
}

//删除物料价格
export function deleteMaterialPriceApi(id){
	return request({
		url:'/scm/base/material-price/delete?id=' + id,
		method:'DELETE',
	})
}